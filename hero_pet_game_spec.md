# 📘 技術需求文檔：英雄孫物冒險遊戲

## 🧱 專案檔構節總覽

```
src/
🔼— Services/                 # Knit 層服務端邏輯
🔼— Controllers/              # Knit 層客戶端控制器
🔼— ECS/                      # Matter 實體系統邏輯
🔼🔼— Components/
🔼🔼— Systems/
🔼— Modules/                  # 模型與靜態資料
🔼— UI/                       # 視覺與介面展示
🔼— DebugTools/               # 調試與測試模組
🔼— Shared/                   # 共用工具、資料格式等
```

---

## 📆 模組說明表

| 分層       | 模組名稱            | 功能概要 |
|------------|---------------------|----------|
| Knit - Service | `PlayerService`      | 處理玩家登入、綁定Profile資料載入 |
| Knit - Service | `PetService`         | 孫物裝備、升級、圖鑑查詢邏輯 |
| Knit - Service | `WeaponService`      | 武器裝備、特效觸發 |
| Knit - Service | `CombatService`      | 攻擊發送與ECS呼叫 |
| Knit - Service | `GachaService`       | 抽卡、金幣扣除與機率控制 |
| Knit - Service | `ZoneService`        | 玩家進出區域感知 ( 安全區/戰鬥區 ) |
| Knit - Controller | `PetController`   | 客戶端孫物介面、圖鑑互動 |
| Knit - Controller | `GachaController` | 抽卡UI動畫與展示邏輯 |
| Knit - Controller | `CombatController`| 攻擊輸入指令監控 |
| Knit - Controller | `UIController`    | UI總控制器：頁面切換、提示彈窗等 |
| ECS - Entity | `PlayerEntity`, `PetEntity`, `MonsterEntity` | 基礎實體定義 |
| ECS - Component | `Position`, `Health`, `PetComponent`, `SwordSwing`... | ECS狀態組件 |
| ECS - System | `PetFollowSystem`, `SwordAttackSystem`... | 執行邏輯運算與狀態變更 |

---

## 📄 資料組織定義 (ProfileService 格式)

```lua
local ProfileTemplate = {
  Coins = 0,
  Gems = 0,
  RBucks = 0,
  Exp = 0,
  OwnedPets = {
    -- [petId] = { level = 1, exp = 0, rarity = "Rare" }
  },
  EquippedPets = { "Slime", "FireDragon" },
  OwnedWeapons = { "BronzeSword", "MagicBlade" },
  GachaHistory = {}, -- 儲存最近10次抽卡結果
}
```

---

## 🧰 系統技術規範

### 🔧 設定檔（.env.exp）

```
DEV_MODE=true
LOG_LEVEL=Debug
LOG_FILE=./app.log
```

### 📜 日誌系統規格

- 模組：`Logger`（共用模組，所有模組引入）
- 格式：`{時間}-{模組}-{級別}-{事件}-{狀態碼}-詳細資訊`
- 級別：`Debug`, `Info`, `Warning`, `Error`
- 寫入規則：
  - 開發模式：覆寫 `./app.log`
  - 生產模式：產出 `{app}/logs/{app}_YYYYMMDD_HHMMSS.log`
- 所有 Service 函數出入口應至少記錄 `Debug` 級別

---

## 🌐 多語系支援 (Fusion + i18n)

### 語系資料組織

`Localization/zh-TW.lua`：

```lua
return {
  UI = {
    StartGame = "開始遊戲",
    GachaTitle = "抽卡",
    PetBook = "孫物圖鑑",
    Confirm = "確認",
    Cancel = "取消",
  },
  Message = {
    NotEnoughCoins = "金幣不足！",
    PetUnlocked = "你解鎖了新孫物：{petName}！",
  },
}
```

### 翻譯模組：`Modules/i18n.lua`

```lua
local i18n = {}
local locale = "zh-TW"
local dictionary = require(game.ReplicatedStorage.Localization[locale])

function i18n.setLocale(lang)
  locale = lang
  dictionary = require(game.ReplicatedStorage.Localization[locale])
end

function i18n.t(path, vars)
  local parts = string.split(path, ".")
  local value = dictionary
  for _, part in ipairs(parts) do
    value = value[part]
    if not value then return path end
  end
  if vars then
    for k, v in pairs(vars) do
      value = value:gsub("{"..k.."}", v)
    end
  end
  return value
end

return i18n
```

### UI 使用方式

```lua
local i18n = require(Modules.i18n)
TextLabel.Text = i18n.t("UI.GachaTitle")
```

---

## ⚙️ Matter ECS 系統邏輯流程

### 系統流程圖：孫物與戰鬥

```
[PlayerEntity]
   │
   └▶ PetComponent (已召喚)
             │
     PetFollowSystem ──▶ Update Pet Position
             │
     PetAttackSystem ──▶ Scan & Lock Nearest Target
             │
        SwordAttackSystem ──▶ Check Hit → DamageComponent
             │
        HealthSystem ──▶ Reduce HP → Check Death → RemoveEntity
```

### 各系統邏輯

#### PetFollowSystem

```lua
local petPos = entity:get(Position)
local ownerPos = entity:get(FollowTarget).position
local dir = (ownerPos - petPos).Unit
entity:set(Position, petPos + dir * speed * deltaTime)
```

#### PetAttackSystem

- 自動掃描附近敵人並設定 TargetComponent
- 每 X 秒啟動一次

#### SwordAttackSystem

- 計算前方攻擊區域
- 命中即對敵人添加 `DamageComponent`

#### HealthSystem

- 讀取 `DamageComponent`，扣除 `HealthComponent.hp`
- HP ≤ 0 時移除實體並標記死亡

#### MonsterAISystem

```lua
State: "Patrol" → 若發現玩家 → "Chase"
State: "Chase" → 接近玩家 → "Attack"
State: "Attack" → 攻擊失敗 → 回 "Chase"
```

---

## 🔄 操作流程抽解

### 🎮 玩家攻擊流程

```
[鍵盤輸入] → CombatController → CombatService → ECS 實體攻擊處理
```

### 🐾 孫物攻擊流程

```
PlayerEntity → PetFollowSystem → PetAttackSystem → SwordAttackSystem → HealthSystem
```

### 💎 抽卡流程

```
GachaController → GachaService → PetDatabase → GachaUI動畫展示
```

