-- Logger 日誌系統
-- 提供統一的日誌記錄功能
-- 格式：{時間}-{模組}-{級別}-{事件}-{狀態碼}-詳細資訊

local Logger = {}

-- 日誌級別
local LogLevel = {
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4
}

-- 日誌級別名稱
local LogLevelNames = {
    [1] = "Debug",
    [2] = "Info", 
    [3] = "Warning",
    [4] = "Error"
}

-- 設定
local config = {
    level = LogLevel.Debug,
    devMode = true,
    logFile = "./app.log"
}

-- 載入環境設定
local function loadConfig()
    -- 這裡可以從 .env.exp 文件載入設定
    -- 暫時使用預設值
end

-- 初始化
loadConfig()

-- 格式化時間
local function formatTime()
    return os.date("%Y-%m-%d %H:%M:%S")
end

-- 格式化日誌訊息
local function formatMessage(module, level, event, statusCode, details)
    local timestamp = formatTime()
    local levelName = LogLevelNames[level] or "Unknown"
    
    return string.format("%s-%s-%s-%s-%s-%s", 
        timestamp, module, levelName, event, tostring(statusCode), tostring(details))
end

-- 輸出日誌
local function output(message)
    print(message)
    
    -- 在開發模式下，也可以寫入文件
    if config.devMode then
        -- 這裡可以實現文件寫入邏輯
        -- 由於Roblox限制，實際實現可能需要使用DataStore或其他方式
    end
end

-- 記錄日誌
local function log(module, level, event, statusCode, details)
    if level >= config.level then
        local message = formatMessage(module, level, event, statusCode, details or "")
        output(message)
    end
end

-- 公開方法
function Logger.debug(module, event, statusCode, details)
    log(module, LogLevel.Debug, event, statusCode, details)
end

function Logger.info(module, event, statusCode, details)
    log(module, LogLevel.Info, event, statusCode, details)
end

function Logger.warning(module, event, statusCode, details)
    log(module, LogLevel.Warning, event, statusCode, details)
end

function Logger.error(module, event, statusCode, details)
    log(module, LogLevel.Error, event, statusCode, details)
end

-- 設定日誌級別
function Logger.setLevel(level)
    if LogLevelNames[level] then
        config.level = level
    end
end

-- 設定開發模式
function Logger.setDevMode(devMode)
    config.devMode = devMode
end

-- 獲取當前設定
function Logger.getConfig()
    return {
        level = config.level,
        levelName = LogLevelNames[config.level],
        devMode = config.devMode,
        logFile = config.logFile
    }
end

-- 創建模組專用的Logger
function Logger.createModuleLogger(moduleName)
    return {
        debug = function(event, statusCode, details)
            Logger.debug(moduleName, event, statusCode, details)
        end,
        info = function(event, statusCode, details)
            Logger.info(moduleName, event, statusCode, details)
        end,
        warning = function(event, statusCode, details)
            Logger.warning(moduleName, event, statusCode, details)
        end,
        error = function(event, statusCode, details)
            Logger.error(moduleName, event, statusCode, details)
        end
    }
end

return Logger
