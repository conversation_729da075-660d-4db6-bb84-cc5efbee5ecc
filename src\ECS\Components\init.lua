-- ECS Components 模組
-- 定義所有遊戲實體的組件

local Matter = require(game:GetService("ReplicatedStorage").Packages.Matter)

local Components = {}

-- 位置組件
Components.Position = Matter.component("Position", {
    x = 0,
    y = 0,
    z = 0
})

-- 生命值組件
Components.Health = Matter.component("Health", {
    current = 100,
    maximum = 100,
    regeneration = 0  -- 每秒回復量
})

-- 傷害組件（臨時組件，用於處理傷害）
Components.Damage = Matter.component("Damage", {
    amount = 0,
    source = nil,  -- 傷害來源實體
    damageType = "Physical"  -- 傷害類型
})

-- 孫物組件
Components.PetComponent = Matter.component("PetComponent", {
    petId = "",
    ownerId = 0,  -- 主人的UserId
    level = 1,
    experience = 0,
    stats = {
        hp = 100,
        attack = 10,
        defense = 5,
        speed = 8
    },
    skills = {},
    isActive = true
})

-- 跟隨目標組件
Components.FollowTarget = Matter.component("FollowTarget", {
    targetId = nil,  -- 目標實體ID
    distance = 5,    -- 跟隨距離
    speed = 10       -- 跟隨速度
})

-- 攻擊目標組件
Components.AttackTarget = Matter.component("AttackTarget", {
    targetId = nil,
    attackRange = 10,
    attackCooldown = 1.0,
    lastAttackTime = 0
})

-- 劍擊攻擊組件
Components.SwordSwing = Matter.component("SwordSwing", {
    damage = 10,
    range = 8,
    angle = 90,      -- 攻擊角度（度）
    duration = 0.5,  -- 攻擊持續時間
    startTime = 0,
    isActive = false
})

-- 移動組件
Components.Movement = Matter.component("Movement", {
    velocity = { x = 0, y = 0, z = 0 },
    speed = 10,
    acceleration = 50,
    friction = 0.9
})

-- 玩家組件
Components.Player = Matter.component("Player", {
    userId = 0,
    name = "",
    level = 1,
    experience = 0
})

-- 怪物組件
Components.Monster = Matter.component("Monster", {
    monsterId = "",
    level = 1,
    aiState = "Patrol",  -- Patrol, Chase, Attack, Dead
    detectionRange = 15,
    attackRange = 3,
    patrolRadius = 10,
    patrolCenter = { x = 0, y = 0, z = 0 },
    lastStateChange = 0
})

-- 武器組件
Components.Weapon = Matter.component("Weapon", {
    weaponId = "",
    damage = 10,
    attackSpeed = 1.0,
    range = 5,
    durability = 100,
    maxDurability = 100,
    enchantments = {}
})

-- 裝備組件
Components.Equipment = Matter.component("Equipment", {
    weapon = nil,     -- 武器實體ID
    armor = nil,      -- 護甲實體ID
    accessories = {}  -- 飾品實體ID列表
})

-- 經驗值獎勵組件
Components.ExperienceReward = Matter.component("ExperienceReward", {
    amount = 10,
    recipients = {}  -- 獲得經驗的實體ID列表
})

-- 戰利品組件
Components.Loot = Matter.component("Loot", {
    items = {},      -- 戰利品列表
    dropChance = 1.0 -- 掉落機率
})

-- 區域組件
Components.Zone = Matter.component("Zone", {
    zoneId = "",
    zoneType = "Safe",  -- Safe, Combat, Boss, PvP
    bounds = {
        min = { x = 0, y = 0, z = 0 },
        max = { x = 100, y = 100, z = 100 }
    },
    properties = {}
})

-- 在區域內組件
Components.InZone = Matter.component("InZone", {
    zoneId = "",
    entryTime = 0
})

-- 冷卻時間組件
Components.Cooldown = Matter.component("Cooldown", {
    name = "",
    duration = 1.0,
    startTime = 0,
    isActive = false
})

-- 狀態效果組件
Components.StatusEffect = Matter.component("StatusEffect", {
    effectType = "",  -- Poison, Burn, Freeze, Stun等
    duration = 5.0,
    startTime = 0,
    intensity = 1,
    tickInterval = 1.0,
    lastTick = 0
})

-- 動畫組件
Components.Animation = Matter.component("Animation", {
    animationId = "",
    isPlaying = false,
    looped = false,
    speed = 1.0,
    startTime = 0
})

-- 音效組件
Components.Sound = Matter.component("Sound", {
    soundId = "",
    volume = 1.0,
    pitch = 1.0,
    isPlaying = false,
    loop = false
})

-- 粒子效果組件
Components.ParticleEffect = Matter.component("ParticleEffect", {
    effectId = "",
    duration = 2.0,
    startTime = 0,
    isActive = false
})

-- 碰撞組件
Components.Collision = Matter.component("Collision", {
    shape = "Box",  -- Box, Sphere, Capsule
    size = { x = 1, y = 1, z = 1 },
    isTrigger = false,
    layer = "Default"
})

-- 可互動組件
Components.Interactable = Matter.component("Interactable", {
    interactionType = "",  -- Chest, NPC, Portal等
    range = 5,
    isActive = true,
    data = {}
})

return Components
