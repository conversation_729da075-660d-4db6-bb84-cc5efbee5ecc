-- i18n 多語系模組
-- 提供遊戲內文字的多語系支援

local i18n = {}

-- 預設語系
local locale = "zh-TW"
local dictionary = nil

-- 載入語系字典
local function loadDictionary()
    local success, result = pcall(function()
        return require(game.ReplicatedStorage.Localization[locale])
    end)
    
    if success then
        dictionary = result
    else
        warn("無法載入語系文件: " .. locale)
        dictionary = {}
    end
end

-- 初始化
loadDictionary()

-- 設定語系
function i18n.setLocale(lang)
    if lang and lang ~= locale then
        locale = lang
        loadDictionary()
    end
end

-- 獲取當前語系
function i18n.getLocale()
    return locale
end

-- 翻譯文字
-- @param path 翻譯路徑，例如 "UI.StartGame"
-- @param vars 變數替換表，例如 {petName = "史萊姆"}
function i18n.t(path, vars)
    if not dictionary then
        return path
    end
    
    -- 分割路徑
    local parts = string.split(path, ".")
    local value = dictionary
    
    -- 遍歷路徑找到對應值
    for _, part in ipairs(parts) do
        if type(value) == "table" and value[part] then
            value = value[part]
        else
            -- 找不到翻譯，返回原路徑
            return path
        end
    end
    
    -- 如果最終值不是字串，返回原路徑
    if type(value) ~= "string" then
        return path
    end
    
    -- 變數替換
    if vars and type(vars) == "table" then
        for k, v in pairs(vars) do
            value = value:gsub("{" .. k .. "}", tostring(v))
        end
    end
    
    return value
end

-- 檢查翻譯是否存在
function i18n.exists(path)
    if not dictionary then
        return false
    end
    
    local parts = string.split(path, ".")
    local value = dictionary
    
    for _, part in ipairs(parts) do
        if type(value) == "table" and value[part] then
            value = value[part]
        else
            return false
        end
    end
    
    return type(value) == "string"
end

-- 獲取所有可用語系
function i18n.getAvailableLocales()
    local locales = {}
    local localizationFolder = game.ReplicatedStorage:FindFirstChild("Localization")
    
    if localizationFolder then
        for _, child in ipairs(localizationFolder:GetChildren()) do
            if child:IsA("ModuleScript") then
                table.insert(locales, child.Name)
            end
        end
    end
    
    return locales
end

return i18n
