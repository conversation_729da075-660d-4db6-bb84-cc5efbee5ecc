# 英雄孫物冒險遊戲 - 設計文檔

## 遊戲概述
一款基於Roblox平台的英雄孫物冒險遊戲，玩家可以收集、培養孫物，並與它們一起戰鬥冒險。

## 核心玩法
1. **孫物收集系統** - 通過抽卡獲得不同稀有度的孫物
2. **戰鬥系統** - 玩家和孫物協同作戰
3. **成長系統** - 孫物升級和裝備強化
4. **探索系統** - 不同區域的冒險

## 技術架構
- **前端框架**: Roblox Studio + Fusion UI
- **狀態管理**: Knit Framework
- **實體系統**: Matter ECS
- **多語系**: 自定義i18n模組
- **日誌系統**: 自定義Logger模組

## 模組設計
### Knit Services (服務端)
- PlayerService: 玩家數據管理
- PetService: 孫物系統
- WeaponService: 武器系統
- CombatService: 戰鬥邏輯
- GachaService: 抽卡系統
- ZoneService: 區域管理

### Knit Controllers (客戶端)
- PetController: 孫物UI控制
- GachaController: 抽卡UI控制
- CombatController: 戰鬥輸入控制
- UIController: 總UI控制器

### ECS 系統
- Components: Position, Health, PetComponent等
- Systems: PetFollowSystem, SwordAttackSystem等

## 數據結構
參考 hero_pet_game_spec.md 中的 ProfileTemplate 定義

## 開發規範
- 所有模組必須包含適當的日誌記錄
- 使用多語系支援
- 遵循ECS架構模式
- 代碼註釋使用繁體中文
