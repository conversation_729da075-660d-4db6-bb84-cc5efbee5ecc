-- Services 模組初始化
-- 載入所有服務模組

local Services = {}

-- 載入所有服務
Services.PlayerService = require(script.PlayerService)
Services.PetService = require(script.PetService)
-- Services.WeaponService = require(script.WeaponService)
-- Services.CombatService = require(script.CombatService)
-- Services.GachaService = require(script.GachaService)
-- Services.ZoneService = require(script.ZoneService)

return Services
