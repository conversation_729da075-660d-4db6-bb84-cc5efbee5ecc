-- PlayerService
-- 處理玩家登入、綁定Profile資料載入

local Knit = require(game:GetService("ReplicatedStorage").Packages.Knit)
local Players = game:GetService("Players")
local DataStoreService = game:GetService("DataStoreService")

local Logger = require(script.Parent.Parent.Shared.Logger)
local ProfileTemplate = require(script.Parent.Parent.Modules.ProfileTemplate)

local PlayerService = Knit.CreateService {
    Name = "PlayerService",
    Client = {
        -- 客戶端可調用的方法
        GetPlayerData = Knit.CreateSignal(),
        PlayerDataUpdated = Knit.CreateSignal(),
    },
}

-- 私有變數
local logger = Logger.createModuleLogger("PlayerService")
local playerDataStore = DataStoreService:GetDataStore("PlayerData")
local playerProfiles = {}  -- 儲存所有玩家的Profile數據

-- 私有方法

-- 載入玩家數據
local function loadPlayerData(player)
    logger.debug("LoadPlayerData", "START", "玩家: " .. player.Name)
    
    local success, data = pcall(function()
        return playerDataStore:GetAsync("Player_" .. player.UserId)
    end)
    
    if success and data then
        -- 驗證數據完整性
        if ProfileTemplate.Validate(data) then
            logger.info("LoadPlayerData", "SUCCESS", "數據載入成功: " .. player.Name)
            return data
        else
            logger.warning("LoadPlayerData", "DATA_CORRUPTED", "數據損壞，嘗試修復: " .. player.Name)
            return ProfileTemplate.Repair(data)
        end
    else
        logger.info("LoadPlayerData", "NEW_PLAYER", "創建新玩家數據: " .. player.Name)
        return ProfileTemplate.CreateNew()
    end
end

-- 儲存玩家數據
local function savePlayerData(player, profile)
    logger.debug("SavePlayerData", "START", "玩家: " .. player.Name)
    
    -- 更新儲存時間
    profile.System.LastSaveTime = tick()
    
    local success, errorMessage = pcall(function()
        playerDataStore:SetAsync("Player_" .. player.UserId, profile)
    end)
    
    if success then
        logger.info("SavePlayerData", "SUCCESS", "數據儲存成功: " .. player.Name)
        return true
    else
        logger.error("SavePlayerData", "FAILED", "數據儲存失敗: " .. player.Name .. " - " .. tostring(errorMessage))
        return false
    end
end

-- 公開方法

-- 服務初始化
function PlayerService:KnitStart()
    logger.info("PlayerService", "INIT_START", "服務初始化開始")
    
    -- 監聽玩家加入事件
    Players.PlayerAdded:Connect(function(player)
        self:OnPlayerJoined(player)
    end)
    
    -- 監聽玩家離開事件
    Players.PlayerRemoving:Connect(function(player)
        self:OnPlayerLeaving(player)
    end)
    
    -- 處理已經在遊戲中的玩家
    for _, player in ipairs(Players:GetPlayers()) do
        self:OnPlayerJoined(player)
    end
    
    logger.info("PlayerService", "INIT_COMPLETE", "服務初始化完成")
end

-- 玩家加入處理
function PlayerService:OnPlayerJoined(player)
    logger.info("OnPlayerJoined", "START", "玩家加入: " .. player.Name)
    
    -- 載入玩家數據
    local profile = loadPlayerData(player)
    playerProfiles[player.UserId] = profile
    
    -- 更新最後登入時間
    profile.Stats.LastLoginTime = tick()
    
    -- 通知客戶端
    self.Client.PlayerDataUpdated:Fire(player, profile)
    
    logger.info("OnPlayerJoined", "COMPLETE", "玩家加入處理完成: " .. player.Name)
end

-- 玩家離開處理
function PlayerService:OnPlayerLeaving(player)
    logger.info("OnPlayerLeaving", "START", "玩家離開: " .. player.Name)
    
    local profile = playerProfiles[player.UserId]
    if profile then
        -- 儲存玩家數據
        savePlayerData(player, profile)
        
        -- 清除記憶體中的數據
        playerProfiles[player.UserId] = nil
    end
    
    logger.info("OnPlayerLeaving", "COMPLETE", "玩家離開處理完成: " .. player.Name)
end

-- 獲取玩家Profile
function PlayerService:GetPlayerProfile(player)
    return playerProfiles[player.UserId]
end

-- 更新玩家數據
function PlayerService:UpdatePlayerData(player, dataPath, value)
    logger.debug("UpdatePlayerData", "START", "玩家: " .. player.Name .. " 路徑: " .. dataPath)
    
    local profile = playerProfiles[player.UserId]
    if not profile then
        logger.error("UpdatePlayerData", "NO_PROFILE", "找不到玩家Profile: " .. player.Name)
        return false
    end
    
    -- 解析數據路徑並更新值
    local pathParts = string.split(dataPath, ".")
    local current = profile
    
    for i = 1, #pathParts - 1 do
        if current[pathParts[i]] then
            current = current[pathParts[i]]
        else
            logger.error("UpdatePlayerData", "INVALID_PATH", "無效的數據路徑: " .. dataPath)
            return false
        end
    end
    
    current[pathParts[#pathParts]] = value
    
    -- 通知客戶端數據更新
    self.Client.PlayerDataUpdated:Fire(player, profile)
    
    logger.info("UpdatePlayerData", "SUCCESS", "數據更新成功: " .. player.Name)
    return true
end

-- 手動儲存玩家數據
function PlayerService:SavePlayerData(player)
    local profile = playerProfiles[player.UserId]
    if profile then
        return savePlayerData(player, profile)
    end
    return false
end

-- 客戶端方法
function PlayerService.Client:GetPlayerData(player)
    return self.Server:GetPlayerProfile(player)
end

return PlayerService
