-- PetFollowSystem
-- 處理孫物跟隨玩家的邏輯

local Matter = require(game:GetService("ReplicatedStorage").Packages.Matter)
local Components = require(script.Parent.Parent.Components)
local Logger = require(script.Parent.Parent.Parent.Shared.Logger)

local logger = Logger.createModuleLogger("PetFollowSystem")

local PetFollowSystem = {}

-- 系統初始化
function PetFollowSystem.new(world)
    logger.debug("PetFollowSystem", "INIT", "系統初始化")
    
    return {
        world = world,
        
        -- 系統更新函數
        step = function(self, deltaTime)
            self:updatePetFollow(deltaTime)
        end,
        
        -- 更新孫物跟隨邏輯
        updatePetFollow = function(self, deltaTime)
            -- 查詢所有有孫物組件和跟隨目標組件的實體
            for entityId, petComponent, followTarget, position in self.world:query(
                Components.PetComponent, 
                Components.FollowTarget, 
                Components.Position
            ) do
                -- 只處理活躍的孫物
                if not petComponent.isActive then
                    continue
                end
                
                -- 找到目標實體的位置
                local targetPosition = self.world:get(followTarget.targetId, Components.Position)
                if not targetPosition then
                    continue
                end
                
                -- 計算距離
                local dx = targetPosition.x - position.x
                local dy = targetPosition.y - position.y
                local dz = targetPosition.z - position.z
                local distance = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                -- 如果距離大於跟隨距離，開始移動
                if distance > followTarget.distance then
                    -- 計算移動方向
                    local dirX = dx / distance
                    local dirY = dy / distance
                    local dirZ = dz / distance
                    
                    -- 計算移動速度（基於孫物的速度屬性）
                    local moveSpeed = petComponent.stats.speed or followTarget.speed
                    
                    -- 如果距離很遠，增加移動速度
                    if distance > followTarget.distance * 3 then
                        moveSpeed = moveSpeed * 2
                    end
                    
                    -- 更新位置
                    local newX = position.x + dirX * moveSpeed * deltaTime
                    local newY = position.y + dirY * moveSpeed * deltaTime
                    local newZ = position.z + dirZ * moveSpeed * deltaTime
                    
                    -- 確保不會超過目標位置
                    local newDistance = math.sqrt(
                        (targetPosition.x - newX)^2 + 
                        (targetPosition.y - newY)^2 + 
                        (targetPosition.z - newZ)^2
                    )
                    
                    if newDistance < followTarget.distance then
                        -- 停在跟隨距離處
                        local stopX = targetPosition.x - dirX * followTarget.distance
                        local stopY = targetPosition.y - dirY * followTarget.distance
                        local stopZ = targetPosition.z - dirZ * followTarget.distance
                        
                        self.world:insert(entityId, Components.Position({
                            x = stopX,
                            y = stopY,
                            z = stopZ
                        }))
                    else
                        -- 正常移動
                        self.world:insert(entityId, Components.Position({
                            x = newX,
                            y = newY,
                            z = newZ
                        }))
                    end
                    
                    -- 添加移動組件以便其他系統知道孫物在移動
                    self.world:insert(entityId, Components.Movement({
                        velocity = {
                            x = dirX * moveSpeed,
                            y = dirY * moveSpeed,
                            z = dirZ * moveSpeed
                        },
                        speed = moveSpeed
                    }))
                else
                    -- 距離足夠近，停止移動
                    if self.world:get(entityId, Components.Movement) then
                        self.world:insert(entityId, Components.Movement({
                            velocity = { x = 0, y = 0, z = 0 },
                            speed = 0
                        }))
                    end
                end
            end
        end,
        
        -- 設置孫物跟隨目標
        setPetFollowTarget = function(self, petEntityId, targetEntityId, distance, speed)
            distance = distance or 5
            speed = speed or 10
            
            self.world:insert(petEntityId, Components.FollowTarget({
                targetId = targetEntityId,
                distance = distance,
                speed = speed
            }))
            
            logger.debug("setPetFollowTarget", "SUCCESS", 
                "孫物 " .. petEntityId .. " 開始跟隨 " .. targetEntityId)
        end,
        
        -- 停止孫物跟隨
        stopPetFollow = function(self, petEntityId)
            self.world:remove(petEntityId, Components.FollowTarget)
            
            -- 停止移動
            if self.world:get(petEntityId, Components.Movement) then
                self.world:insert(petEntityId, Components.Movement({
                    velocity = { x = 0, y = 0, z = 0 },
                    speed = 0
                }))
            end
            
            logger.debug("stopPetFollow", "SUCCESS", "孫物 " .. petEntityId .. " 停止跟隨")
        end,
        
        -- 傳送孫物到目標附近
        teleportPetToTarget = function(self, petEntityId, targetEntityId, offset)
            offset = offset or { x = 3, y = 0, z = 3 }
            
            local targetPosition = self.world:get(targetEntityId, Components.Position)
            if not targetPosition then
                logger.warning("teleportPetToTarget", "NO_TARGET", "找不到目標位置")
                return false
            end
            
            self.world:insert(petEntityId, Components.Position({
                x = targetPosition.x + offset.x,
                y = targetPosition.y + offset.y,
                z = targetPosition.z + offset.z
            }))
            
            logger.debug("teleportPetToTarget", "SUCCESS", 
                "孫物 " .. petEntityId .. " 傳送到目標附近")
            return true
        end,
        
        -- 檢查孫物是否離目標太遠
        checkPetDistance = function(self, petEntityId, targetEntityId, maxDistance)
            maxDistance = maxDistance or 50
            
            local petPosition = self.world:get(petEntityId, Components.Position)
            local targetPosition = self.world:get(targetEntityId, Components.Position)
            
            if not petPosition or not targetPosition then
                return false
            end
            
            local distance = math.sqrt(
                (petPosition.x - targetPosition.x)^2 +
                (petPosition.y - targetPosition.y)^2 +
                (petPosition.z - targetPosition.z)^2
            )
            
            return distance > maxDistance
        end
    }
end

return PetFollowSystem
