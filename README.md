# 英雄孫物冒險遊戲

一款基於Roblox平台的英雄孫物冒險遊戲，玩家可以收集、培養孫物，並與它們一起戰鬥冒險。

## 專案結構

```
src/
├── Services/                 # Knit 層服務端邏輯
│   ├── PlayerService.lua    # 玩家數據管理
│   ├── PetService.lua       # 孫物系統
│   └── init.lua            # 服務初始化
├── Controllers/              # Knit 層客戶端控制器
│   ├── PetController.lua    # 孫物UI控制
│   └── init.lua            # 控制器初始化
├── ECS/                      # Matter 實體系統邏輯
│   ├── Components/          # ECS組件定義
│   │   └── init.lua
│   └── Systems/             # ECS系統邏輯
│       ├── PetFollowSystem.lua
│       └── init.lua
├── Modules/                  # 模型與靜態資料
│   ├── i18n.lua             # 多語系模組
│   └── ProfileTemplate.lua  # 玩家數據模板
├── UI/                       # 視覺與介面展示
├── DebugTools/               # 調試與測試模組
├── Shared/                   # 共用工具、資料格式等
│   └── Logger.lua           # 日誌系統
└── init.lua                 # 主初始化文件
```

## 技術架構

- **前端框架**: Roblox Studio + Fusion UI
- **狀態管理**: Knit Framework
- **實體系統**: Matter ECS
- **多語系**: 自定義i18n模組
- **日誌系統**: 自定義Logger模組

## 核心功能

### 已實現
- [x] 基礎專案架構
- [x] 日誌系統
- [x] 多語系支援
- [x] 玩家數據管理 (PlayerService)
- [x] 孫物系統基礎 (PetService)
- [x] 孫物跟隨系統 (ECS)
- [x] 孫物圖鑑UI (PetController)

### 計劃中
- [ ] 戰鬥系統
- [ ] 武器系統
- [ ] 抽卡系統
- [ ] 區域系統
- [ ] 怪物AI
- [ ] 完整UI系統

## 開發指南

### 環境設置
1. 確保已安裝Roblox Studio
2. 安裝必要的包：Knit, Matter
3. 將專案文件放入ReplicatedStorage

### 配置文件
編輯 `.env.exp` 文件來調整開發設置：
```
DEV_MODE=true
LOG_LEVEL=Debug
LOG_FILE=./app.log
```

### 日誌系統使用
```lua
local Logger = require(script.Parent.Shared.Logger)
local logger = Logger.createModuleLogger("ModuleName")

logger.debug("FunctionName", "START", "詳細信息")
logger.info("FunctionName", "SUCCESS", "操作成功")
logger.warning("FunctionName", "WARNING", "警告信息")
logger.error("FunctionName", "ERROR", "錯誤信息")
```

### 多語系使用
```lua
local i18n = require(script.Parent.Modules.i18n)

-- 基本翻譯
local text = i18n.t("UI.StartGame")  -- "開始遊戲"

-- 帶變數的翻譯
local message = i18n.t("Message.PetUnlocked", {petName = "史萊姆"})
-- "你解鎖了新孫物：史萊姆！"
```

### ECS系統開發
```lua
-- 創建新組件
local MyComponent = Matter.component("MyComponent", {
    value = 0
})

-- 創建新系統
local MySystem = {}
function MySystem.new(world)
    return {
        step = function(self, deltaTime)
            for entityId, component in world:query(MyComponent) do
                -- 系統邏輯
            end
        end
    }
end
```

## 數據結構

### 玩家Profile
```lua
{
    Coins = 0,           -- 金幣
    Gems = 0,            -- 寶石
    RBucks = 0,          -- R幣
    Exp = 0,             -- 經驗值
    Level = 1,           -- 等級
    OwnedPets = {},      -- 擁有的孫物
    EquippedPets = {},   -- 裝備的孫物
    OwnedWeapons = {},   -- 擁有的武器
    -- ... 更多欄位
}
```

### 孫物數據
```lua
{
    level = 1,
    exp = 0,
    rarity = "Common",
    stats = {
        hp = 100,
        attack = 10,
        defense = 5,
        speed = 8
    }
}
```

## 貢獻指南

1. 遵循現有的代碼風格
2. 所有新功能都要包含適當的日誌記錄
3. 使用多語系支援
4. 遵循ECS架構模式
5. 代碼註釋使用繁體中文

## 授權

此專案僅供學習和開發使用。
