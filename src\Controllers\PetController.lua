-- PetController
-- 客戶端孫物介面、圖鑑互動

local Knit = require(game:GetService("ReplicatedStorage").Packages.Knit)
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local Logger = require(script.Parent.Parent.Shared.Logger)
local i18n = require(script.Parent.Parent.Modules.i18n)

local PetController = Knit.CreateController {
    Name = "PetController"
}

-- 私有變數
local logger = Logger.createModuleLogger("PetController")
local player = Players.LocalPlayer
local PetService
local UIController

-- UI元素
local petBookGui = nil
local petInventoryGui = nil
local currentPetData = {}

-- 私有方法

-- 創建孫物圖鑑UI
local function createPetBookUI()
    logger.debug("createPetBookUI", "START", "創建孫物圖鑑UI")
    
    -- 創建主要GUI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "PetBookGui"
    screenGui.Parent = player.PlayerGui
    screenGui.Enabled = false
    
    -- 主框架
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0.8, 0, 0.8, 0)
    mainFrame.Position = UDim2.new(0.1, 0, 0.1, 0)
    mainFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = screenGui
    
    -- 標題
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, 0, 0.1, 0)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = i18n.t("UI.PetBook")
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- 關閉按鈕
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0.1, 0, 0.08, 0)
    closeButton.Position = UDim2.new(0.88, 0, 0.02, 0)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    -- 孫物列表滾動框
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "PetScrollFrame"
    scrollFrame.Size = UDim2.new(0.45, 0, 0.8, 0)
    scrollFrame.Position = UDim2.new(0.02, 0, 0.15, 0)
    scrollFrame.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 10
    scrollFrame.Parent = mainFrame
    
    -- 孫物詳情框
    local detailFrame = Instance.new("Frame")
    detailFrame.Name = "DetailFrame"
    detailFrame.Size = UDim2.new(0.5, 0, 0.8, 0)
    detailFrame.Position = UDim2.new(0.48, 0, 0.15, 0)
    detailFrame.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
    detailFrame.BorderSizePixel = 0
    detailFrame.Parent = mainFrame
    
    -- 綁定關閉按鈕事件
    closeButton.MouseButton1Click:Connect(function()
        PetController:ClosePetBook()
    end)
    
    petBookGui = screenGui
    logger.info("createPetBookUI", "SUCCESS", "孫物圖鑑UI創建完成")
end

-- 更新孫物列表
local function updatePetList()
    if not petBookGui then return end
    
    local scrollFrame = petBookGui.MainFrame.PetScrollFrame
    
    -- 清除現有內容
    for _, child in ipairs(scrollFrame:GetChildren()) do
        if child:IsA("GuiObject") then
            child:Destroy()
        end
    end
    
    -- 獲取孫物數據
    local playerPets = PetService:GetPlayerPets()
    local petDatabase = PetService:GetPetDatabase()
    
    local yOffset = 0
    local itemHeight = 80
    
    for petId, petInfo in pairs(playerPets) do
        local petData = petDatabase[petId]
        if petData then
            -- 創建孫物項目
            local petItem = Instance.new("Frame")
            petItem.Name = "PetItem_" .. petId
            petItem.Size = UDim2.new(1, -20, 0, itemHeight)
            petItem.Position = UDim2.new(0, 10, 0, yOffset)
            petItem.BackgroundColor3 = Color3.fromRGB(90, 90, 90)
            petItem.BorderSizePixel = 1
            petItem.BorderColor3 = Color3.fromRGB(150, 150, 150)
            petItem.Parent = scrollFrame
            
            -- 孫物名稱
            local nameLabel = Instance.new("TextLabel")
            nameLabel.Size = UDim2.new(0.6, 0, 0.4, 0)
            nameLabel.Position = UDim2.new(0.05, 0, 0.1, 0)
            nameLabel.BackgroundTransparency = 1
            nameLabel.Text = i18n.t("Pet." .. petId) or petData.name
            nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
            nameLabel.TextScaled = true
            nameLabel.Font = Enum.Font.SourceSans
            nameLabel.TextXAlignment = Enum.TextXAlignment.Left
            nameLabel.Parent = petItem
            
            -- 等級和稀有度
            local infoLabel = Instance.new("TextLabel")
            infoLabel.Size = UDim2.new(0.6, 0, 0.3, 0)
            infoLabel.Position = UDim2.new(0.05, 0, 0.5, 0)
            infoLabel.BackgroundTransparency = 1
            infoLabel.Text = string.format("Lv.%d | %s", petInfo.level, i18n.t("Rarity." .. petInfo.rarity))
            infoLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
            infoLabel.TextScaled = true
            infoLabel.Font = Enum.Font.SourceSans
            infoLabel.TextXAlignment = Enum.TextXAlignment.Left
            infoLabel.Parent = petItem
            
            -- 裝備按鈕
            local equipButton = Instance.new("TextButton")
            equipButton.Size = UDim2.new(0.25, 0, 0.6, 0)
            equipButton.Position = UDim2.new(0.7, 0, 0.2, 0)
            equipButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
            equipButton.Text = i18n.t("UI.Equip", "裝備")
            equipButton.TextColor3 = Color3.fromRGB(255, 255, 255)
            equipButton.TextScaled = true
            equipButton.Font = Enum.Font.SourceSans
            equipButton.Parent = petItem
            
            -- 綁定點擊事件
            equipButton.MouseButton1Click:Connect(function()
                PetController:EquipPet(petId)
            end)
            
            petItem.MouseButton1Click:Connect(function()
                PetController:ShowPetDetails(petId)
            end)
            
            yOffset = yOffset + itemHeight + 5
        end
    end
    
    -- 更新滾動框大小
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset)
end

-- 顯示孫物詳情
local function showPetDetails(petId)
    if not petBookGui then return end
    
    local detailFrame = petBookGui.MainFrame.DetailFrame
    
    -- 清除現有內容
    for _, child in ipairs(detailFrame:GetChildren()) do
        if child:IsA("GuiObject") then
            child:Destroy()
        end
    end
    
    local playerPets = PetService:GetPlayerPets()
    local petDatabase = PetService:GetPetDatabase()
    
    local petInfo = playerPets[petId]
    local petData = petDatabase[petId]
    
    if not petInfo or not petData then return end
    
    -- 孫物名稱
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(1, 0, 0.1, 0)
    nameLabel.Position = UDim2.new(0, 0, 0.05, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = i18n.t("Pet." .. petId) or petData.name
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.Parent = detailFrame
    
    -- 等級和經驗
    local levelLabel = Instance.new("TextLabel")
    levelLabel.Size = UDim2.new(1, 0, 0.08, 0)
    levelLabel.Position = UDim2.new(0, 0, 0.18, 0)
    levelLabel.BackgroundTransparency = 1
    levelLabel.Text = string.format("等級: %d | 經驗: %d", petInfo.level, petInfo.exp)
    levelLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    levelLabel.TextScaled = true
    levelLabel.Font = Enum.Font.SourceSans
    levelLabel.Parent = detailFrame
    
    -- 屬性顯示
    local statsFrame = Instance.new("Frame")
    statsFrame.Size = UDim2.new(1, 0, 0.4, 0)
    statsFrame.Position = UDim2.new(0, 0, 0.3, 0)
    statsFrame.BackgroundTransparency = 1
    statsFrame.Parent = detailFrame
    
    local stats = {"hp", "attack", "defense", "speed"}
    local statNames = {"生命值", "攻擊力", "防禦力", "速度"}
    
    for i, stat in ipairs(stats) do
        local statLabel = Instance.new("TextLabel")
        statLabel.Size = UDim2.new(1, 0, 0.2, 0)
        statLabel.Position = UDim2.new(0, 0, (i-1) * 0.22, 0)
        statLabel.BackgroundTransparency = 1
        statLabel.Text = string.format("%s: %d", statNames[i], petInfo.stats[stat] or 0)
        statLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        statLabel.TextScaled = true
        statLabel.Font = Enum.Font.SourceSans
        statLabel.TextXAlignment = Enum.TextXAlignment.Left
        statLabel.Parent = statsFrame
    end
end

-- 公開方法

-- 控制器初始化
function PetController:KnitStart()
    logger.info("PetController", "INIT_START", "控制器初始化開始")
    
    -- 獲取服務引用
    PetService = Knit.GetService("PetService")
    UIController = Knit.GetController("UIController")
    
    -- 創建UI
    createPetBookUI()
    
    -- 監聽服務事件
    PetService.PetEquipped:Connect(function(petId)
        self:OnPetEquipped(petId)
    end)
    
    PetService.PetLevelUp:Connect(function(petId, newLevel)
        self:OnPetLevelUp(petId, newLevel)
    end)
    
    PetService.PetUnlocked:Connect(function(petId)
        self:OnPetUnlocked(petId)
    end)
    
    logger.info("PetController", "INIT_COMPLETE", "控制器初始化完成")
end

-- 打開孫物圖鑑
function PetController:OpenPetBook()
    logger.debug("OpenPetBook", "START", "打開孫物圖鑑")
    
    if petBookGui then
        petBookGui.Enabled = true
        updatePetList()
    end
end

-- 關閉孫物圖鑑
function PetController:ClosePetBook()
    logger.debug("ClosePetBook", "START", "關閉孫物圖鑑")
    
    if petBookGui then
        petBookGui.Enabled = false
    end
end

-- 裝備孫物
function PetController:EquipPet(petId)
    logger.debug("EquipPet", "START", "裝備孫物: " .. petId)
    
    PetService:EquipPet(petId)
end

-- 顯示孫物詳情
function PetController:ShowPetDetails(petId)
    showPetDetails(petId)
end

-- 事件處理
function PetController:OnPetEquipped(petId)
    logger.info("OnPetEquipped", "SUCCESS", "孫物裝備成功: " .. petId)
    updatePetList()
end

function PetController:OnPetLevelUp(petId, newLevel)
    logger.info("OnPetLevelUp", "SUCCESS", "孫物升級: " .. petId .. " 等級: " .. newLevel)
    updatePetList()
end

function PetController:OnPetUnlocked(petId)
    logger.info("OnPetUnlocked", "SUCCESS", "解鎖新孫物: " .. petId)
    updatePetList()
end

return PetController
