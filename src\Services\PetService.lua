-- PetService
-- 孫物裝備、升級、圖鑑查詢邏輯

local Knit = require(game:GetService("ReplicatedStorage").Packages.Knit)
local Logger = require(script.Parent.Parent.Shared.Logger)

local PetService = Knit.CreateService {
    Name = "PetService",
    Client = {
        -- 客戶端可調用的方法
        PetEquipped = Knit.CreateSignal(),
        PetLevelUp = Knit.CreateSignal(),
        PetUnlocked = Knit.CreateSignal(),
    },
}

-- 私有變數
local logger = Logger.createModuleLogger("PetService")
local PlayerService

-- 孫物數據庫
local PetDatabase = {
    Slime = {
        name = "史萊姆",
        rarity = "Common",
        baseStats = { hp = 80, attack = 8, defense = 4, speed = 6 },
        maxLevel = 50,
        expToNext = function(level) return level * 100 end,
        skills = { "Bounce", "Absorb" }
    },
    FireDragon = {
        name = "火龍",
        rarity = "Legendary",
        baseStats = { hp = 150, attack = 20, defense = 12, speed = 10 },
        maxLevel = 100,
        expToNext = function(level) return level * 200 end,
        skills = { "FireBreath", "DragonRoar", "FlameWing" }
    },
    WaterSpirit = {
        name = "水精靈",
        rarity = "Rare",
        baseStats = { hp = 100, attack = 12, defense = 8, speed = 14 },
        maxLevel = 75,
        expToNext = function(level) return level * 150 end,
        skills = { "WaterBolt", "Heal", "AquaShield" }
    },
    EarthGolem = {
        name = "土石魔像",
        rarity = "Epic",
        baseStats = { hp = 200, attack = 15, defense = 20, speed = 4 },
        maxLevel = 80,
        expToNext = function(level) return level * 180 end,
        skills = { "RockThrow", "EarthQuake", "StoneArmor" }
    }
}

-- 私有方法

-- 計算孫物等級對應的屬性
local function calculatePetStats(petId, level)
    local petData = PetDatabase[petId]
    if not petData then
        return nil
    end
    
    local multiplier = 1 + (level - 1) * 0.1  -- 每級增加10%屬性
    
    return {
        hp = math.floor(petData.baseStats.hp * multiplier),
        attack = math.floor(petData.baseStats.attack * multiplier),
        defense = math.floor(petData.baseStats.defense * multiplier),
        speed = math.floor(petData.baseStats.speed * multiplier)
    }
end

-- 檢查孫物是否可以升級
local function canLevelUp(petData, petInfo)
    if petInfo.level >= petData.maxLevel then
        return false
    end
    
    local expRequired = petData.expToNext(petInfo.level)
    return petInfo.exp >= expRequired
end

-- 公開方法

-- 服務初始化
function PetService:KnitStart()
    logger.info("PetService", "INIT_START", "服務初始化開始")
    
    -- 獲取PlayerService引用
    PlayerService = Knit.GetService("PlayerService")
    
    logger.info("PetService", "INIT_COMPLETE", "服務初始化完成")
end

-- 獲取孫物數據庫
function PetService:GetPetDatabase()
    return PetDatabase
end

-- 獲取玩家擁有的孫物
function PetService:GetPlayerPets(player)
    logger.debug("GetPlayerPets", "START", "玩家: " .. player.Name)
    
    local profile = PlayerService:GetPlayerProfile(player)
    if not profile then
        logger.error("GetPlayerPets", "NO_PROFILE", "找不到玩家Profile")
        return {}
    end
    
    return profile.OwnedPets
end

-- 裝備孫物
function PetService:EquipPet(player, petId)
    logger.debug("EquipPet", "START", "玩家: " .. player.Name .. " 孫物: " .. petId)
    
    local profile = PlayerService:GetPlayerProfile(player)
    if not profile then
        logger.error("EquipPet", "NO_PROFILE", "找不到玩家Profile")
        return false
    end
    
    -- 檢查玩家是否擁有此孫物
    if not profile.OwnedPets[petId] then
        logger.warning("EquipPet", "NOT_OWNED", "玩家未擁有此孫物: " .. petId)
        return false
    end
    
    -- 檢查是否已經裝備
    for _, equippedPetId in ipairs(profile.EquippedPets) do
        if equippedPetId == petId then
            logger.info("EquipPet", "ALREADY_EQUIPPED", "孫物已經裝備: " .. petId)
            return true
        end
    end
    
    -- 裝備孫物（最多2隻）
    if #profile.EquippedPets < 2 then
        table.insert(profile.EquippedPets, petId)
    else
        -- 替換第一隻孫物
        profile.EquippedPets[1] = petId
    end
    
    -- 通知客戶端
    self.Client.PetEquipped:Fire(player, petId)
    
    logger.info("EquipPet", "SUCCESS", "孫物裝備成功: " .. petId)
    return true
end

-- 卸下孫物
function PetService:UnequipPet(player, petId)
    logger.debug("UnequipPet", "START", "玩家: " .. player.Name .. " 孫物: " .. petId)
    
    local profile = PlayerService:GetPlayerProfile(player)
    if not profile then
        return false
    end
    
    -- 從裝備列表中移除
    for i, equippedPetId in ipairs(profile.EquippedPets) do
        if equippedPetId == petId then
            table.remove(profile.EquippedPets, i)
            logger.info("UnequipPet", "SUCCESS", "孫物卸下成功: " .. petId)
            return true
        end
    end
    
    logger.warning("UnequipPet", "NOT_EQUIPPED", "孫物未裝備: " .. petId)
    return false
end

-- 孫物升級
function PetService:LevelUpPet(player, petId)
    logger.debug("LevelUpPet", "START", "玩家: " .. player.Name .. " 孫物: " .. petId)
    
    local profile = PlayerService:GetPlayerProfile(player)
    if not profile or not profile.OwnedPets[petId] then
        return false
    end
    
    local petInfo = profile.OwnedPets[petId]
    local petData = PetDatabase[petId]
    
    if not petData then
        logger.error("LevelUpPet", "INVALID_PET", "無效的孫物ID: " .. petId)
        return false
    end
    
    -- 檢查是否可以升級
    if not canLevelUp(petData, petInfo) then
        logger.info("LevelUpPet", "CANNOT_LEVELUP", "孫物無法升級: " .. petId)
        return false
    end
    
    -- 扣除經驗值並升級
    local expRequired = petData.expToNext(petInfo.level)
    petInfo.exp = petInfo.exp - expRequired
    petInfo.level = petInfo.level + 1
    
    -- 更新屬性
    petInfo.stats = calculatePetStats(petId, petInfo.level)
    
    -- 通知客戶端
    self.Client.PetLevelUp:Fire(player, petId, petInfo.level)
    
    logger.info("LevelUpPet", "SUCCESS", "孫物升級成功: " .. petId .. " 等級: " .. petInfo.level)
    return true
end

-- 添加孫物經驗值
function PetService:AddPetExp(player, petId, expAmount)
    local profile = PlayerService:GetPlayerProfile(player)
    if not profile or not profile.OwnedPets[petId] then
        return false
    end
    
    local petInfo = profile.OwnedPets[petId]
    petInfo.exp = petInfo.exp + expAmount
    
    -- 檢查是否可以自動升級
    local petData = PetDatabase[petId]
    while canLevelUp(petData, petInfo) do
        self:LevelUpPet(player, petId)
    end
    
    return true
end

-- 解鎖新孫物
function PetService:UnlockPet(player, petId, rarity)
    logger.debug("UnlockPet", "START", "玩家: " .. player.Name .. " 孫物: " .. petId)
    
    local profile = PlayerService:GetPlayerProfile(player)
    if not profile then
        return false
    end
    
    -- 檢查是否已經擁有
    if profile.OwnedPets[petId] then
        logger.info("UnlockPet", "ALREADY_OWNED", "玩家已擁有此孫物: " .. petId)
        return false
    end
    
    -- 添加到擁有列表
    profile.OwnedPets[petId] = {
        level = 1,
        exp = 0,
        rarity = rarity or "Common",
        unlockTime = tick(),
        stats = calculatePetStats(petId, 1)
    }
    
    -- 更新統計
    profile.Stats.PetsCollected = profile.Stats.PetsCollected + 1
    
    -- 通知客戶端
    self.Client.PetUnlocked:Fire(player, petId)
    
    logger.info("UnlockPet", "SUCCESS", "孫物解鎖成功: " .. petId)
    return true
end

-- 客戶端方法
function PetService.Client:GetPlayerPets(player)
    return self.Server:GetPlayerPets(player)
end

function PetService.Client:EquipPet(player, petId)
    return self.Server:EquipPet(player, petId)
end

function PetService.Client:UnequipPet(player, petId)
    return self.Server:UnequipPet(player, petId)
end

function PetService.Client:GetPetDatabase(player)
    return self.Server:GetPetDatabase()
end

return PetService
