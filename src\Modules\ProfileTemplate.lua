-- 玩家數據模板
-- 定義玩家Profile的數據結構

local ProfileTemplate = {
    -- 基礎貨幣
    Coins = 0,          -- 金幣
    Gems = 0,           -- 寶石
    RBucks = 0,         -- R幣
    
    -- 玩家等級和經驗
    Exp = 0,            -- 經驗值
    Level = 1,          -- 等級
    
    -- 孫物相關
    OwnedPets = {
        -- [petId] = { 
        --     level = 1, 
        --     exp = 0, 
        --     rarity = "Common",
        --     unlockTime = tick(),
        --     stats = {
        --         hp = 100,
        --         attack = 10,
        --         defense = 5,
        --         speed = 8
        --     }
        -- }
    },
    EquippedPets = { "Slime", "FireDragon" },  -- 裝備的孫物ID列表
    
    -- 武器相關
    OwnedWeapons = { "BronzeSword", "MagicBlade" },  -- 擁有的武器ID列表
    EquippedWeapon = "BronzeSword",  -- 當前裝備的武器
    
    -- 抽卡相關
    GachaHistory = {},  -- 儲存最近10次抽卡結果
    GachaTokens = 0,    -- 抽卡代幣
    
    -- 遊戲進度
    CurrentZone = "SafeZone",  -- 當前所在區域
    UnlockedZones = { "SafeZone" },  -- 已解鎖的區域
    CompletedQuests = {},  -- 已完成的任務
    
    -- 統計數據
    Stats = {
        TotalPlayTime = 0,      -- 總遊戲時間（秒）
        MonstersKilled = 0,     -- 擊殺怪物數量
        GachaCount = 0,         -- 抽卡次數
        PetsCollected = 0,      -- 收集的孫物數量
        HighestLevel = 1,       -- 最高等級
        LastLoginTime = 0,      -- 最後登入時間
    },
    
    -- 設定
    Settings = {
        Language = "zh-TW",     -- 語言設定
        SoundEnabled = true,    -- 音效開關
        MusicEnabled = true,    -- 音樂開關
        AutoSave = true,        -- 自動儲存
        ShowTutorial = true,    -- 顯示教學
    },
    
    -- 系統數據
    System = {
        Version = "1.0.0",      -- 數據版本
        CreatedTime = 0,        -- 創建時間
        LastSaveTime = 0,       -- 最後儲存時間
        DataIntegrity = true,   -- 數據完整性
    }
}

-- 創建新的玩家Profile
local function createNewProfile()
    local newProfile = {}
    
    -- 深拷貝模板
    for key, value in pairs(ProfileTemplate) do
        if type(value) == "table" then
            newProfile[key] = {}
            for subKey, subValue in pairs(value) do
                if type(subValue) == "table" then
                    newProfile[key][subKey] = {}
                    for subSubKey, subSubValue in pairs(subValue) do
                        newProfile[key][subKey][subSubKey] = subSubValue
                    end
                else
                    newProfile[key][subKey] = subValue
                end
            end
        else
            newProfile[key] = value
        end
    end
    
    -- 設定創建時間
    local currentTime = tick()
    newProfile.System.CreatedTime = currentTime
    newProfile.System.LastSaveTime = currentTime
    newProfile.Stats.LastLoginTime = currentTime
    
    return newProfile
end

-- 驗證Profile數據完整性
local function validateProfile(profile)
    if not profile or type(profile) ~= "table" then
        return false
    end
    
    -- 檢查必要欄位
    local requiredFields = {
        "Coins", "Gems", "RBucks", "Exp", "Level",
        "OwnedPets", "EquippedPets", "OwnedWeapons",
        "Stats", "Settings", "System"
    }
    
    for _, field in ipairs(requiredFields) do
        if profile[field] == nil then
            return false
        end
    end
    
    return true
end

-- 修復損壞的Profile數據
local function repairProfile(profile)
    local repairedProfile = createNewProfile()
    
    if profile and type(profile) == "table" then
        -- 保留有效的數據
        for key, value in pairs(profile) do
            if repairedProfile[key] ~= nil then
                if type(value) == type(repairedProfile[key]) then
                    repairedProfile[key] = value
                end
            end
        end
    end
    
    return repairedProfile
end

return {
    Template = ProfileTemplate,
    CreateNew = createNewProfile,
    Validate = validateProfile,
    Repair = repairProfile
}
